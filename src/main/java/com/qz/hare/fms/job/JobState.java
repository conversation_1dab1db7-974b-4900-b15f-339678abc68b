package com.qz.hare.fms.job;

/**
 * Job状态枚举 - 用于Spring状态机
 * 
 * <AUTHOR>
 * create at 2025/8/19
 */
public enum JobState {
    /**
     * 编辑中 - 初始状态
     */
    EDITING,
    
    /**
     * 调度失败
     */
    SCHEDULE_FAILED,
    
    /**
     * 启动中 - 将任务分配给engine，并且engine还未运行起来任务时的状态
     */
    WAITING_START,
    
    /**
     * 等待下次运行 - 定时执行任务用户启动后，等待运行时的状态
     */
    WAITING_NEXT_RUN,
    
    /**
     * 运行中 - 正在运行的任务
     */
    RUNNING,
    
    /**
     * 停止中 - 用户点击停止任务后，引擎还没有停下任务之前
     */
    STOPPING,
    
    /**
     * 运行完成 - 全量任务运行完成后的状态
     */
    COMPLETE,
    
    /**
     * 已停止 - 用户点击停止任务，引擎已经将任务停下来
     */
    STOPPED,
    
    /**
     * 错误
     */
    ERROR,
    
    /**
     * 重置中
     */
    RESETTING,
    
    /**
     * 重置失败
     */
    RESET_FAILED,
    
    /**
     * 已删除
     */
    DELETED
}

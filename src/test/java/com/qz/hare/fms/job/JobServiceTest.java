package com.qz.hare.fms.job;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.statemachine.state.State;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * JobService单元测试
 * 
 * <AUTHOR>
 * create at 2025/8/19
 */
@ExtendWith(MockitoExtension.class)
class JobServiceTest {
    
    @Mock
    private JobRepository jobRepository;
    
    @Mock
    private StateMachineFactory<JobState, JobEvent> stateMachineFactory;
    
    @Mock
    private StateMachinePersister<JobState, JobEvent, String> stateMachinePersister;
    
    @Mock
    private StateMachine<JobState, JobEvent> stateMachine;
    
    @Mock
    private State<JobState, JobEvent> state;
    
    @InjectMocks
    private JobService jobService;
    
    private Job testJob;
    
    @BeforeEach
    void setUp() {
        testJob = new Job("Test Job", "Test Description");
        testJob.setId("test-job-id");
        testJob.setCreator("test-user");
        testJob.setStateMachineId("test-sm-id");
    }
    
    @Test
    void testCreateJob() {
        // Given
        when(jobRepository.save(any(Job.class))).thenReturn(testJob);
        
        // When
        Job result = jobService.createJob("Test Job", "Test Description", "test-user");
        
        // Then
        assertNotNull(result);
        assertEquals("Test Job", result.getName());
        assertEquals("Test Description", result.getDescription());
        assertEquals("test-user", result.getCreator());
        assertEquals(JobState.EDITING, result.getCurrentState());
        verify(jobRepository).save(any(Job.class));
    }
    
    @Test
    void testGetAllJobs() {
        // Given
        List<Job> jobs = Arrays.asList(testJob, new Job("Job 2", "Description 2"));
        when(jobRepository.findAll()).thenReturn(jobs);
        
        // When
        List<Job> result = jobService.getAllJobs();
        
        // Then
        assertEquals(2, result.size());
        verify(jobRepository).findAll();
    }
    
    @Test
    void testGetJobById() {
        // Given
        when(jobRepository.findById("test-job-id")).thenReturn(Optional.of(testJob));
        
        // When
        Optional<Job> result = jobService.getJobById("test-job-id");
        
        // Then
        assertTrue(result.isPresent());
        assertEquals(testJob, result.get());
        verify(jobRepository).findById("test-job-id");
    }
    
    @Test
    void testGetJobsByState() {
        // Given
        List<Job> jobs = Arrays.asList(testJob);
        when(jobRepository.findByCurrentState(JobState.EDITING)).thenReturn(jobs);
        
        // When
        List<Job> result = jobService.getJobsByState(JobState.EDITING);
        
        // Then
        assertEquals(1, result.size());
        assertEquals(testJob, result.get(0));
        verify(jobRepository).findByCurrentState(JobState.EDITING);
    }
    
    @Test
    void testSendEventSuccess() throws Exception {
        // Given
        when(jobRepository.findById("test-job-id")).thenReturn(Optional.of(testJob));
        when(stateMachineFactory.getStateMachine()).thenReturn(stateMachine);
        when(stateMachine.sendEvent(JobEvent.SUBMIT)).thenReturn(true);
        when(stateMachine.getState()).thenReturn(state);
        when(state.getId()).thenReturn(JobState.WAITING_START);
        when(jobRepository.save(any(Job.class))).thenReturn(testJob);
        
        // When
        boolean result = jobService.sendEvent("test-job-id", JobEvent.SUBMIT);
        
        // Then
        assertTrue(result);
        verify(stateMachinePersister).restore(stateMachine, "test-job-id");
        verify(stateMachine).sendEvent(JobEvent.SUBMIT);
        verify(stateMachinePersister).persist(stateMachine, "test-job-id");
        verify(jobRepository).save(any(Job.class));
    }
    
    @Test
    void testSendEventJobNotFound() {
        // Given
        when(jobRepository.findById("non-existent-id")).thenReturn(Optional.empty());
        
        // When
        boolean result = jobService.sendEvent("non-existent-id", JobEvent.SUBMIT);
        
        // Then
        assertFalse(result);
        verify(jobRepository).findById("non-existent-id");
        verifyNoInteractions(stateMachineFactory);
    }
    
    @Test
    void testSubmitJob() throws Exception {
        // Given
        when(jobRepository.findById("test-job-id")).thenReturn(Optional.of(testJob));
        when(stateMachineFactory.getStateMachine()).thenReturn(stateMachine);
        when(stateMachine.sendEvent(JobEvent.SUBMIT)).thenReturn(true);
        when(stateMachine.getState()).thenReturn(state);
        when(state.getId()).thenReturn(JobState.WAITING_START);
        when(jobRepository.save(any(Job.class))).thenReturn(testJob);
        
        // When
        boolean result = jobService.submitJob("test-job-id");
        
        // Then
        assertTrue(result);
        verify(stateMachine).sendEvent(JobEvent.SUBMIT);
    }
    
    @Test
    void testStartJob() throws Exception {
        // Given
        when(jobRepository.findById("test-job-id")).thenReturn(Optional.of(testJob));
        when(stateMachineFactory.getStateMachine()).thenReturn(stateMachine);
        when(stateMachine.sendEvent(any(JobEvent.class))).thenReturn(true);
        when(stateMachine.getState()).thenReturn(state);
        when(state.getId()).thenReturn(JobState.RUNNING);
        when(jobRepository.save(any(Job.class))).thenReturn(testJob);
        
        // When
        boolean result = jobService.startJob("test-job-id");
        
        // Then
        assertTrue(result);
        verify(stateMachine).sendEvent(JobEvent.SCHEDULE_SUCCESS);
        verify(stateMachine).sendEvent(JobEvent.RUN);
    }
    
    @Test
    void testCompleteJob() throws Exception {
        // Given
        when(jobRepository.findById("test-job-id")).thenReturn(Optional.of(testJob));
        when(stateMachineFactory.getStateMachine()).thenReturn(stateMachine);
        when(stateMachine.sendEvent(JobEvent.FINISH)).thenReturn(true);
        when(stateMachine.getState()).thenReturn(state);
        when(state.getId()).thenReturn(JobState.COMPLETE);
        when(jobRepository.save(any(Job.class))).thenReturn(testJob);
        
        // When
        boolean result = jobService.completeJob("test-job-id");
        
        // Then
        assertTrue(result);
        verify(stateMachine).sendEvent(JobEvent.FINISH);
        // 验证运行次数增加
        assertEquals(1, testJob.getRunCount());
        assertNotNull(testJob.getLastRunTime());
    }
    
    @Test
    void testErrorJob() throws Exception {
        // Given
        String errorMessage = "Test error message";
        when(jobRepository.findById("test-job-id")).thenReturn(Optional.of(testJob));
        when(stateMachineFactory.getStateMachine()).thenReturn(stateMachine);
        when(stateMachine.sendEvent(JobEvent.ERROR)).thenReturn(true);
        when(stateMachine.getState()).thenReturn(state);
        when(state.getId()).thenReturn(JobState.ERROR);
        when(jobRepository.save(any(Job.class))).thenReturn(testJob);
        
        // When
        boolean result = jobService.errorJob("test-job-id", errorMessage);
        
        // Then
        assertTrue(result);
        verify(stateMachine).sendEvent(JobEvent.ERROR);
        assertEquals(errorMessage, testJob.getErrorMessage());
    }
    
    @Test
    void testDeleteJob() {
        // When
        jobService.deleteJob("test-job-id");
        
        // Then
        verify(jobRepository).deleteById("test-job-id");
    }
}

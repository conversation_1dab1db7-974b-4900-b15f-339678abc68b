package com.qz.hare.fms.job;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 支持并发的Job服务层
 * 
 * <AUTHOR>
 * create at 2025/8/19
 */
@Service("concurrentJobService")
public class ConcurrentJobService {
    
    private static final Logger logger = LoggerFactory.getLogger(ConcurrentJobService.class);
    
    @Autowired
    private JobRepository jobRepository;
    
    @Autowired
    private MongoTemplate mongoTemplate;
    
    @Autowired
    private StateMachineFactory<JobState, JobEvent> stateMachineFactory;
    
    @Autowired
    private StateMachinePersister<JobState, JobEvent, String> stateMachinePersister;
    
    // 每个Job的锁，防止同一个Job的并发操作
    private final ConcurrentHashMap<String, ReentrantLock> jobLocks = new ConcurrentHashMap<>();
    
    // 最大重试次数
    private static final int MAX_RETRY_ATTEMPTS = 3;
    
    /**
     * 获取Job的锁
     */
    private ReentrantLock getJobLock(String jobId) {
        return jobLocks.computeIfAbsent(jobId, k -> new ReentrantLock());
    }
    
    /**
     * 创建新任务
     */
    public Job createJob(String name, String description, String creator) {
        Job job = new Job(name, description);
        job.setCreator(creator);
        job.setStateMachineId(UUID.randomUUID().toString());
        job.setVersion(0L); // 初始版本号
        
        Job savedJob = jobRepository.save(job);
        logger.info("Created new job: {}", savedJob);
        
        return savedJob;
    }
    
    /**
     * 获取所有任务
     */
    public List<Job> getAllJobs() {
        return jobRepository.findAll();
    }
    
    /**
     * 根据ID获取任务
     */
    public Optional<Job> getJobById(String id) {
        return jobRepository.findById(id);
    }
    
    /**
     * 根据状态获取任务列表
     */
    public List<Job> getJobsByState(JobState state) {
        return jobRepository.findByCurrentState(state);
    }
    
    /**
     * 删除任务
     */
    public void deleteJob(String id) {
        jobRepository.deleteById(id);
        // 清理锁
        jobLocks.remove(id);
        logger.info("Deleted job with id: {}", id);
    }
    
    /**
     * 发送事件到状态机 - 并发安全版本
     */
    public boolean sendEvent(String jobId, JobEvent event) {
        ReentrantLock lock = getJobLock(jobId);
        lock.lock();
        
        try {
            return sendEventWithRetry(jobId, event, MAX_RETRY_ATTEMPTS);
        } finally {
            lock.unlock();
        }
    }
    
    /**
     * 带重试的事件发送
     */
    private boolean sendEventWithRetry(String jobId, JobEvent event, int remainingAttempts) {
        if (remainingAttempts <= 0) {
            logger.error("Max retry attempts exceeded for job {} event {}", jobId, event);
            return false;
        }
        
        try {
            // 1. 获取当前Job状态（带版本号）
            Optional<Job> jobOpt = jobRepository.findById(jobId);
            if (jobOpt.isEmpty()) {
                logger.warn("Job not found: {}", jobId);
                return false;
            }
            
            Job job = jobOpt.get();
            Long currentVersion = job.getVersion();
            
            // 2. 创建状态机实例
            StateMachine<JobState, JobEvent> stateMachine = stateMachineFactory.getStateMachine();
            
            // 3. 恢复状态机状态
            stateMachinePersister.restore(stateMachine, jobId);
            
            // 4. 发送事件
            boolean result = stateMachine.sendEvent(event);
            
            if (result) {
                // 5. 使用乐观锁更新状态
                JobState newState = stateMachine.getState().getId();
                boolean updateSuccess = updateJobStateWithOptimisticLock(
                    jobId, currentVersion, newState, job);
                
                if (updateSuccess) {
                    // 6. 持久化状态机状态
                    stateMachinePersister.persist(stateMachine, jobId);
                    
                    logger.info("Event {} sent to job {}, new state: {}", event, jobId, newState);
                    return true;
                } else {
                    // 乐观锁冲突，重试
                    logger.warn("Optimistic lock conflict for job {}, retrying...", jobId);
                    return sendEventWithRetry(jobId, event, remainingAttempts - 1);
                }
            } else {
                logger.warn("Failed to send event {} to job {}", event, jobId);
                return false;
            }
            
        } catch (Exception e) {
            logger.error("Error sending event {} to job {}: {}", event, jobId, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 使用乐观锁更新Job状态
     */
    private boolean updateJobStateWithOptimisticLock(String jobId, Long expectedVersion, 
                                                   JobState newState, Job originalJob) {
        try {
            Query query = new Query(Criteria.where("id").is(jobId)
                                          .and("version").is(expectedVersion));
            
            Update update = new Update()
                .set("currentState", newState)
                .set("updateTime", LocalDateTime.now())
                .inc("version", 1);
            
            // 如果是特定事件，更新额外字段
            if (newState == JobState.COMPLETE) {
                update.set("lastRunTime", LocalDateTime.now())
                      .inc("runCount", 1);
            }
            
            var result = mongoTemplate.updateFirst(query, update, Job.class);
            
            return result.getModifiedCount() > 0;
            
        } catch (OptimisticLockingFailureException e) {
            logger.warn("Optimistic locking failure for job {}", jobId);
            return false;
        }
    }
    
    /**
     * 批量状态转换 - 支持事务
     */
    public boolean batchSendEvent(List<String> jobIds, JobEvent event) {
        boolean allSuccess = true;
        
        // 按jobId排序，避免死锁
        jobIds.sort(String::compareTo);
        
        for (String jobId : jobIds) {
            if (!sendEvent(jobId, event)) {
                allSuccess = false;
                logger.warn("Failed to send event {} to job {}", event, jobId);
            }
        }
        
        return allSuccess;
    }
    
    // 以下方法委托给sendEvent，保持API兼容性
    
    public boolean submitJob(String jobId) {
        return sendEvent(jobId, JobEvent.SUBMIT);
    }
    
    public boolean startJob(String jobId) {
        if (sendEvent(jobId, JobEvent.SCHEDULE_SUCCESS)) {
            return sendEvent(jobId, JobEvent.RUN);
        }
        return false;
    }
    
    public boolean stopJob(String jobId) {
        if (sendEvent(jobId, JobEvent.STOP)) {
            return sendEvent(jobId, JobEvent.STOP_COMPLETE);
        }
        return false;
    }
    
    public boolean completeJob(String jobId) {
        return sendEvent(jobId, JobEvent.FINISH);
    }
    
    public boolean errorJob(String jobId, String errorMessage) {
        // 先更新错误信息
        updateJobErrorMessage(jobId, errorMessage);
        return sendEvent(jobId, JobEvent.ERROR);
    }
    
    public boolean resetJob(String jobId) {
        if (sendEvent(jobId, JobEvent.RESET)) {
            // 清理Job数据
            resetJobData(jobId);
            return sendEvent(jobId, JobEvent.RESET_COMPLETE);
        }
        return false;
    }
    
    public boolean retryJob(String jobId) {
        return sendEvent(jobId, JobEvent.RETRY);
    }
    
    public boolean editJob(String jobId) {
        return sendEvent(jobId, JobEvent.EDIT);
    }
    
    public boolean deleteJobByEvent(String jobId) {
        return sendEvent(jobId, JobEvent.DELETE);
    }
    
    /**
     * 更新错误信息
     */
    private void updateJobErrorMessage(String jobId, String errorMessage) {
        Query query = new Query(Criteria.where("id").is(jobId));
        Update update = new Update().set("errorMessage", errorMessage);
        mongoTemplate.updateFirst(query, update, Job.class);
    }
    
    /**
     * 重置Job数据
     */
    private void resetJobData(String jobId) {
        Query query = new Query(Criteria.where("id").is(jobId));
        Update update = new Update()
            .unset("errorMessage")
            .set("runCount", 0)
            .unset("lastRunTime");
        mongoTemplate.updateFirst(query, update, Job.class);
    }
}

package com.qz.hare.fms.job;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 并发JobService测试
 * 
 * <AUTHOR>
 * create at 2025/8/19
 */
@SpringBootTest
@Testcontainers
class ConcurrentJobServiceTest {
    
    @Container
    static MongoDBContainer mongoDBContainer = new MongoDBContainer("mongo:4.4.6");
    
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.data.mongodb.uri", mongoDBContainer::getReplicaSetUrl);
    }
    
    @Autowired
    @Qualifier("concurrentJobService")
    private ConcurrentJobService jobService;
    
    @Autowired
    private JobRepository jobRepository;
    
    private Job testJob;
    
    @BeforeEach
    void setUp() {
        // 清理数据库
        jobRepository.deleteAll();
        
        // 创建测试任务
        testJob = jobService.createJob("并发测试任务", "测试并发操作", "test-user");
    }
    
    @Test
    void testConcurrentStateTransitions() throws InterruptedException {
        int threadCount = 10;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        
        // 多个线程同时尝试提交同一个任务
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    boolean result = jobService.submitJob(testJob.getId());
                    if (result) {
                        successCount.incrementAndGet();
                    } else {
                        failureCount.incrementAndGet();
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(10, TimeUnit.SECONDS);
        executor.shutdown();
        
        // 验证结果：只有一个线程应该成功
        assertEquals(1, successCount.get(), "只有一个线程应该成功提交任务");
        assertEquals(threadCount - 1, failureCount.get(), "其他线程应该失败");
        
        // 验证最终状态
        Job updatedJob = jobService.getJobById(testJob.getId()).orElse(null);
        assertNotNull(updatedJob);
        assertEquals(JobState.WAITING_START, updatedJob.getCurrentState());
    }
    
    @Test
    void testConcurrentDifferentOperations() throws InterruptedException {
        // 先提交任务
        jobService.submitJob(testJob.getId());
        jobService.startJob(testJob.getId());
        
        int threadCount = 5;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        List<Future<Boolean>> futures = new ArrayList<>();
        
        // 不同线程执行不同操作
        futures.add(executor.submit(() -> {
            try {
                return jobService.completeJob(testJob.getId());
            } finally {
                latch.countDown();
            }
        }));
        
        futures.add(executor.submit(() -> {
            try {
                return jobService.stopJob(testJob.getId());
            } finally {
                latch.countDown();
            }
        }));
        
        futures.add(executor.submit(() -> {
            try {
                return jobService.errorJob(testJob.getId(), "并发错误");
            } finally {
                latch.countDown();
            }
        }));
        
        futures.add(executor.submit(() -> {
            try {
                return jobService.completeJob(testJob.getId());
            } finally {
                latch.countDown();
            }
        }));
        
        futures.add(executor.submit(() -> {
            try {
                return jobService.stopJob(testJob.getId());
            } finally {
                latch.countDown();
            }
        }));
        
        latch.await(10, TimeUnit.SECONDS);
        executor.shutdown();
        
        // 验证至少有一个操作成功
        long successCount = futures.stream()
            .mapToLong(future -> {
                try {
                    return future.get() ? 1 : 0;
                } catch (Exception e) {
                    return 0;
                }
            })
            .sum();
        
        assertTrue(successCount >= 1, "至少应该有一个操作成功");
        
        // 验证最终状态一致性
        Job finalJob = jobService.getJobById(testJob.getId()).orElse(null);
        assertNotNull(finalJob);
        assertNotNull(finalJob.getCurrentState());
        
        // 状态应该是有效的终态之一
        assertTrue(
            finalJob.getCurrentState() == JobState.COMPLETE ||
            finalJob.getCurrentState() == JobState.STOPPED ||
            finalJob.getCurrentState() == JobState.ERROR,
            "最终状态应该是有效的终态"
        );
    }
    
    @Test
    void testBatchOperations() {
        // 创建多个任务
        List<String> jobIds = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            Job job = jobService.createJob("批量任务" + i, "批量测试", "test-user");
            jobIds.add(job.getId());
        }
        
        // 批量提交
        boolean result = jobService.batchSendEvent(jobIds, JobEvent.SUBMIT);
        assertTrue(result, "批量操作应该成功");
        
        // 验证所有任务状态
        for (String jobId : jobIds) {
            Job job = jobService.getJobById(jobId).orElse(null);
            assertNotNull(job);
            assertEquals(JobState.WAITING_START, job.getCurrentState());
        }
    }
    
    @Test
    void testOptimisticLockingRetry() throws InterruptedException {
        // 提交任务到运行状态
        jobService.submitJob(testJob.getId());
        jobService.startJob(testJob.getId());
        
        int threadCount = 20;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        
        // 多个线程同时尝试完成任务
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    boolean result = jobService.completeJob(testJob.getId());
                    if (result) {
                        successCount.incrementAndGet();
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(15, TimeUnit.SECONDS);
        executor.shutdown();
        
        // 验证只有一个线程成功
        assertEquals(1, successCount.get(), "只有一个线程应该成功完成任务");
        
        // 验证最终状态和运行次数
        Job finalJob = jobService.getJobById(testJob.getId()).orElse(null);
        assertNotNull(finalJob);
        assertEquals(JobState.COMPLETE, finalJob.getCurrentState());
        assertEquals(1, finalJob.getRunCount(), "运行次数应该正确递增");
    }
    
    @Test
    void testDeadlockPrevention() throws InterruptedException {
        // 创建两个任务
        Job job1 = jobService.createJob("任务1", "死锁测试", "test-user");
        Job job2 = jobService.createJob("任务2", "死锁测试", "test-user");
        
        ExecutorService executor = Executors.newFixedThreadPool(2);
        CountDownLatch latch = new CountDownLatch(2);
        
        // 线程1：job1 -> job2
        executor.submit(() -> {
            try {
                jobService.submitJob(job1.getId());
                Thread.sleep(100); // 模拟处理时间
                jobService.submitJob(job2.getId());
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                latch.countDown();
            }
        });
        
        // 线程2：job2 -> job1
        executor.submit(() -> {
            try {
                jobService.submitJob(job2.getId());
                Thread.sleep(100); // 模拟处理时间
                jobService.submitJob(job1.getId());
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                latch.countDown();
            }
        });
        
        // 应该在合理时间内完成，不会死锁
        boolean completed = latch.await(5, TimeUnit.SECONDS);
        assertTrue(completed, "操作应该在合理时间内完成，不应该发生死锁");
        
        executor.shutdown();
    }
}

package com.qz.hare.fms.job;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.config.EnableStateMachineFactory;
import org.springframework.statemachine.config.StateMachineConfigurerAdapter;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.listener.StateMachineListener;
import org.springframework.statemachine.listener.StateMachineListenerAdapter;
import org.springframework.statemachine.state.State;
import org.springframework.statemachine.transition.Transition;

/**
 * Job状态机配置
 *
 * <AUTHOR>
 * create at 2025/8/19
 */
@Configuration
@EnableStateMachineFactory
public class JobStateMachineConfig extends StateMachineConfigurerAdapter<JobState, JobEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(JobStateMachineConfig.class);
    
    @Override
    public void configure(StateMachineStateConfigurer<JobState, JobEvent> states) throws Exception {
        states
            .withStates()
                .initial(JobState.EDITING)
                .states(java.util.EnumSet.allOf(JobState.class))
                .end(JobState.DELETED)
                .end(JobState.COMPLETE);
    }
    
    @Override
    public void configure(StateMachineTransitionConfigurer<JobState, JobEvent> transitions) throws Exception {
        transitions
            // 从编辑状态开始
            .withExternal()
                .source(JobState.EDITING).target(JobState.WAITING_START)
                .event(JobEvent.SUBMIT)
                .action(context -> logger.info("Job submitted for execution"))
            .and()
            
            // 启动流程
            .withExternal()
                .source(JobState.WAITING_START).target(JobState.WAITING_NEXT_RUN)
                .event(JobEvent.SCHEDULE_SUCCESS)
                .action(context -> logger.info("Job scheduled successfully"))
            .and()
            .withExternal()
                .source(JobState.WAITING_START).target(JobState.SCHEDULE_FAILED)
                .event(JobEvent.SCHEDULE_FAIL)
                .action(context -> logger.warn("Job scheduling failed"))
            .and()
            
            // 运行流程
            .withExternal()
                .source(JobState.WAITING_NEXT_RUN).target(JobState.RUNNING)
                .event(JobEvent.RUN)
                .action(context -> logger.info("Job started running"))
            .and()
            .withExternal()
                .source(JobState.RUNNING).target(JobState.COMPLETE)
                .event(JobEvent.FINISH)
                .action(context -> logger.info("Job completed successfully"))
            .and()
            .withExternal()
                .source(JobState.RUNNING).target(JobState.WAITING_NEXT_RUN)
                .event(JobEvent.FINISH)
                .action(context -> logger.info("Job finished, waiting for next run"))
            .and()
            
            // 停止流程
            .withExternal()
                .source(JobState.RUNNING).target(JobState.STOPPING)
                .event(JobEvent.STOP)
                .action(context -> logger.info("Job stopping requested"))
            .and()
            .withExternal()
                .source(JobState.WAITING_NEXT_RUN).target(JobState.STOPPING)
                .event(JobEvent.STOP)
                .action(context -> logger.info("Job stopping requested"))
            .and()
            .withExternal()
                .source(JobState.STOPPING).target(JobState.STOPPED)
                .event(JobEvent.STOP_COMPLETE)
                .action(context -> logger.info("Job stopped successfully"))
            .and()
            
            // 错误处理
            .withExternal()
                .source(JobState.RUNNING).target(JobState.ERROR)
                .event(JobEvent.ERROR)
                .action(context -> logger.error("Job encountered an error"))
            .and()
            .withExternal()
                .source(JobState.WAITING_START).target(JobState.ERROR)
                .event(JobEvent.ERROR)
                .action(context -> logger.error("Job encountered an error during startup"))
            .and()
            .withExternal()
                .source(JobState.WAITING_NEXT_RUN).target(JobState.ERROR)
                .event(JobEvent.ERROR)
                .action(context -> logger.error("Job encountered an error while waiting"))
            .and()
            
            // 重置流程
            .withExternal()
                .source(JobState.ERROR).target(JobState.RESETTING)
                .event(JobEvent.RESET)
                .action(context -> logger.info("Job reset requested"))
            .and()
            .withExternal()
                .source(JobState.STOPPED).target(JobState.RESETTING)
                .event(JobEvent.RESET)
                .action(context -> logger.info("Job reset requested"))
            .and()
            .withExternal()
                .source(JobState.COMPLETE).target(JobState.RESETTING)
                .event(JobEvent.RESET)
                .action(context -> logger.info("Job reset requested"))
            .and()
            .withExternal()
                .source(JobState.RESETTING).target(JobState.EDITING)
                .event(JobEvent.RESET_COMPLETE)
                .action(context -> logger.info("Job reset completed"))
            .and()
            .withExternal()
                .source(JobState.RESETTING).target(JobState.RESET_FAILED)
                .event(JobEvent.RESET_FAIL)
                .action(context -> logger.warn("Job reset failed"))
            .and()
            
            // 重新编辑
            .withExternal()
                .source(JobState.STOPPED).target(JobState.EDITING)
                .event(JobEvent.EDIT)
                .action(context -> logger.info("Job returned to editing state"))
            .and()
            .withExternal()
                .source(JobState.ERROR).target(JobState.EDITING)
                .event(JobEvent.EDIT)
                .action(context -> logger.info("Job returned to editing state"))
            .and()
            
            // 重试
            .withExternal()
                .source(JobState.ERROR).target(JobState.WAITING_START)
                .event(JobEvent.RETRY)
                .action(context -> logger.info("Job retry requested"))
            .and()
            .withExternal()
                .source(JobState.SCHEDULE_FAILED).target(JobState.WAITING_START)
                .event(JobEvent.RETRY)
                .action(context -> logger.info("Job retry requested"))
            .and()
            
            // 删除
            .withExternal()
                .source(JobState.EDITING).target(JobState.DELETED)
                .event(JobEvent.DELETE)
                .action(context -> logger.info("Job deleted"))
            .and()
            .withExternal()
                .source(JobState.STOPPED).target(JobState.DELETED)
                .event(JobEvent.DELETE)
                .action(context -> logger.info("Job deleted"))
            .and()
            .withExternal()
                .source(JobState.ERROR).target(JobState.DELETED)
                .event(JobEvent.DELETE)
                .action(context -> logger.info("Job deleted"))
            .and()
            .withExternal()
                .source(JobState.COMPLETE).target(JobState.DELETED)
                .event(JobEvent.DELETE)
                .action(context -> logger.info("Job deleted"));
    }
    
    @Bean
    public StateMachineListener<JobState, JobEvent> stateMachineListener() {
        return new StateMachineListenerAdapter<JobState, JobEvent>() {
            @Override
            public void stateChanged(State<JobState, JobEvent> from, State<JobState, JobEvent> to) {
                logger.info("State changed from {} to {}", 
                    from != null ? from.getId() : "null", 
                    to != null ? to.getId() : "null");
            }
            
            @Override
            public void transition(Transition<JobState, JobEvent> transition) {
                logger.info("Transition: {} -> {} on event {}", 
                    transition.getSource().getId(),
                    transition.getTarget().getId(),
                    transition.getTrigger().getEvent());
            }
        };
    }
}

package com.qz.hare.fms.job;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Job数据访问层
 * 
 * <AUTHOR>
 * create at 2025/8/19
 */
@Repository
public interface JobRepository extends MongoRepository<Job, String> {
    
    /**
     * 根据状态查找任务
     */
    List<Job> findByCurrentState(JobState state);
    
    /**
     * 根据状态机ID查找任务
     */
    Optional<Job> findByStateMachineId(String stateMachineId);
    
    /**
     * 根据名称查找任务
     */
    Optional<Job> findByName(String name);
    
    /**
     * 根据创建者查找任务
     */
    List<Job> findByCreator(String creator);
    
    /**
     * 根据任务类型查找任务
     */
    List<Job> findByJobType(String jobType);
}

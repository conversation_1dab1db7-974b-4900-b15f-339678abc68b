package com.qz.hare.fms.job;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * JobController单元测试
 * 
 * <AUTHOR>
 * create at 2025/8/19
 */
@WebMvcTest(JobController.class)
class JobControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private JobService jobService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private Job testJob;
    
    @BeforeEach
    void setUp() {
        testJob = new Job("Test Job", "Test Description");
        testJob.setId("test-job-id");
        testJob.setCreator("test-user");
    }
    
    @Test
    void testGetAllJobs() throws Exception {
        // Given
        List<Job> jobs = Arrays.asList(testJob, new Job("Job 2", "Description 2"));
        when(jobService.getAllJobs()).thenReturn(jobs);
        
        // When & Then
        mockMvc.perform(get("/api/job"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].name").value("Test Job"))
                .andExpect(jsonPath("$[0].description").value("Test Description"));
    }
    
    @Test
    void testGetJobById() throws Exception {
        // Given
        when(jobService.getJobById("test-job-id")).thenReturn(Optional.of(testJob));
        
        // When & Then
        mockMvc.perform(get("/api/job/test-job-id"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value("test-job-id"))
                .andExpect(jsonPath("$.name").value("Test Job"))
                .andExpect(jsonPath("$.description").value("Test Description"));
    }
    
    @Test
    void testGetJobByIdNotFound() throws Exception {
        // Given
        when(jobService.getJobById("non-existent-id")).thenReturn(Optional.empty());
        
        // When & Then
        mockMvc.perform(get("/api/job/non-existent-id"))
                .andExpect(status().isNotFound());
    }
    
    @Test
    void testCreateJob() throws Exception {
        // Given
        JobController.CreateJobRequest request = new JobController.CreateJobRequest();
        request.setName("New Job");
        request.setDescription("New Description");
        request.setCreator("new-user");
        
        Job newJob = new Job("New Job", "New Description");
        newJob.setId("new-job-id");
        newJob.setCreator("new-user");
        
        when(jobService.createJob("New Job", "New Description", "new-user")).thenReturn(newJob);
        
        // When & Then
        mockMvc.perform(post("/api/job")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.name").value("New Job"))
                .andExpect(jsonPath("$.description").value("New Description"))
                .andExpect(jsonPath("$.creator").value("new-user"));
    }
    
    @Test
    void testSubmitJob() throws Exception {
        // Given
        when(jobService.submitJob("test-job-id")).thenReturn(true);
        
        // When & Then
        mockMvc.perform(post("/api/job/test-job-id/submit"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Job submitted successfully"));
    }
    
    @Test
    void testStartJob() throws Exception {
        // Given
        when(jobService.startJob("test-job-id")).thenReturn(true);
        
        // When & Then
        mockMvc.perform(post("/api/job/test-job-id/start"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Job started successfully"));
    }
    
    @Test
    void testStopJob() throws Exception {
        // Given
        when(jobService.stopJob("test-job-id")).thenReturn(true);
        
        // When & Then
        mockMvc.perform(post("/api/job/test-job-id/stop"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Job stopped successfully"));
    }
    
    @Test
    void testCompleteJob() throws Exception {
        // Given
        when(jobService.completeJob("test-job-id")).thenReturn(true);
        
        // When & Then
        mockMvc.perform(post("/api/job/test-job-id/complete"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Job completed successfully"));
    }
    
    @Test
    void testErrorJob() throws Exception {
        // Given
        when(jobService.errorJob(anyString(), anyString())).thenReturn(true);
        
        // When & Then
        mockMvc.perform(post("/api/job/test-job-id/error")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"errorMessage\": \"Test error\"}"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Job error recorded"));
    }
    
    @Test
    void testResetJob() throws Exception {
        // Given
        when(jobService.resetJob("test-job-id")).thenReturn(true);
        
        // When & Then
        mockMvc.perform(post("/api/job/test-job-id/reset"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Job reset successfully"));
    }
    
    @Test
    void testRetryJob() throws Exception {
        // Given
        when(jobService.retryJob("test-job-id")).thenReturn(true);
        
        // When & Then
        mockMvc.perform(post("/api/job/test-job-id/retry"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Job retry initiated"));
    }
    
    @Test
    void testEditJob() throws Exception {
        // Given
        when(jobService.editJob("test-job-id")).thenReturn(true);
        
        // When & Then
        mockMvc.perform(post("/api/job/test-job-id/edit"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Job returned to editing state"));
    }
    
    @Test
    void testGetJobsByState() throws Exception {
        // Given
        List<Job> jobs = Arrays.asList(testJob);
        when(jobService.getJobsByState(JobState.EDITING)).thenReturn(jobs);
        
        // When & Then
        mockMvc.perform(get("/api/job/state/EDITING"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].name").value("Test Job"));
    }
}

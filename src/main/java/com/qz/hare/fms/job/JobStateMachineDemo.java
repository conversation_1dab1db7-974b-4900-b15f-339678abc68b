package com.qz.hare.fms.job;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * Job状态机演示程序
 * 
 * <AUTHOR>
 * create at 2025/8/19
 */
@Component
public class JobStateMachineDemo implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(JobStateMachineDemo.class);
    
    @Autowired
    private JobService jobService;
    
    @Override
    public void run(String... args) throws Exception {
        logger.info("=== Job状态机演示开始 ===");
        
        // 创建一个新任务
        Job job = jobService.createJob("演示任务", "这是一个演示Spring状态机的任务", "demo-user");
        logger.info("创建任务: {}", job);
        
        // 演示完整的任务生命周期
        demonstrateJobLifecycle(job.getId());
        
        // 演示错误处理
        demonstrateErrorHandling(job.getId());
        
        logger.info("=== Job状态机演示结束 ===");
    }
    
    private void demonstrateJobLifecycle(String jobId) {
        logger.info("\n--- 演示任务生命周期 ---");
        
        // 1. 提交任务
        logger.info("1. 提交任务...");
        jobService.submitJob(jobId);
        printJobState(jobId);
        
        // 2. 启动任务
        logger.info("2. 启动任务...");
        jobService.startJob(jobId);
        printJobState(jobId);
        
        // 3. 完成任务
        logger.info("3. 完成任务...");
        jobService.completeJob(jobId);
        printJobState(jobId);
        
        // 4. 重置任务
        logger.info("4. 重置任务...");
        jobService.resetJob(jobId);
        printJobState(jobId);
    }
    
    private void demonstrateErrorHandling(String jobId) {
        logger.info("\n--- 演示错误处理 ---");
        
        // 1. 提交并启动任务
        logger.info("1. 提交并启动任务...");
        jobService.submitJob(jobId);
        jobService.startJob(jobId);
        printJobState(jobId);
        
        // 2. 模拟任务出错
        logger.info("2. 模拟任务出错...");
        jobService.errorJob(jobId, "模拟的错误信息");
        printJobState(jobId);
        
        // 3. 重试任务
        logger.info("3. 重试任务...");
        jobService.retryJob(jobId);
        printJobState(jobId);
        
        // 4. 再次启动并停止
        logger.info("4. 启动任务...");
        jobService.startJob(jobId);
        printJobState(jobId);
        
        logger.info("5. 停止任务...");
        jobService.stopJob(jobId);
        printJobState(jobId);
        
        // 5. 返回编辑状态
        logger.info("6. 返回编辑状态...");
        jobService.editJob(jobId);
        printJobState(jobId);
    }
    
    private void printJobState(String jobId) {
        jobService.getJobById(jobId).ifPresent(job -> {
            logger.info("   当前状态: {} | 运行次数: {} | 错误信息: {}", 
                job.getCurrentState(), 
                job.getRunCount(), 
                job.getErrorMessage());
        });
    }
}

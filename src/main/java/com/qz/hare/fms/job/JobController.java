package com.qz.hare.fms.job;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Job控制器 - 提供Job的REST API
 *
 * <AUTHOR>
 * create at 2025/8/19 14:50
 */
@RestController
@RequestMapping("/api/job")
public class JobController {

    @Autowired
    private JobService jobService;

    /**
     * 获取所有任务
     */
    @GetMapping
    public ResponseEntity<List<Job>> getAllJobs() {
        List<Job> jobs = jobService.getAllJobs();
        return ResponseEntity.ok(jobs);
    }

    /**
     * 根据ID获取任务
     */
    @GetMapping("/{id}")
    public ResponseEntity<Job> getJobById(@PathVariable String id) {
        Optional<Job> job = jobService.getJobById(id);
        return job.map(ResponseEntity::ok)
                  .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 创建新任务
     */
    @PostMapping
    public ResponseEntity<Job> createJob(@RequestBody CreateJobRequest request) {
        Job job = jobService.createJob(request.getName(), request.getDescription(), request.getCreator());
        return ResponseEntity.ok(job);
    }

    /**
     * 更新任务
     */
    @PutMapping("/{id}")
    public ResponseEntity<Job> updateJob(@PathVariable String id, @RequestBody Job job) {
        if (!id.equals(job.getId())) {
            return ResponseEntity.badRequest().build();
        }
        Job updatedJob = jobService.updateJob(job);
        return ResponseEntity.ok(updatedJob);
    }

    /**
     * 删除任务
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteJob(@PathVariable String id) {
        jobService.deleteJob(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 提交任务
     */
    @PostMapping("/{id}/submit")
    public ResponseEntity<Map<String, Object>> submitJob(@PathVariable String id) {
        boolean success = jobService.submitJob(id);
        return ResponseEntity.ok(Map.of("success", success, "message",
            success ? "Job submitted successfully" : "Failed to submit job"));
    }

    /**
     * 启动任务
     */
    @PostMapping("/{id}/start")
    public ResponseEntity<Map<String, Object>> startJob(@PathVariable String id) {
        boolean success = jobService.startJob(id);
        return ResponseEntity.ok(Map.of("success", success, "message",
            success ? "Job started successfully" : "Failed to start job"));
    }

    /**
     * 停止任务
     */
    @PostMapping("/{id}/stop")
    public ResponseEntity<Map<String, Object>> stopJob(@PathVariable String id) {
        boolean success = jobService.stopJob(id);
        return ResponseEntity.ok(Map.of("success", success, "message",
            success ? "Job stopped successfully" : "Failed to stop job"));
    }

    /**
     * 完成任务
     */
    @PostMapping("/{id}/complete")
    public ResponseEntity<Map<String, Object>> completeJob(@PathVariable String id) {
        boolean success = jobService.completeJob(id);
        return ResponseEntity.ok(Map.of("success", success, "message",
            success ? "Job completed successfully" : "Failed to complete job"));
    }

    /**
     * 任务出错
     */
    @PostMapping("/{id}/error")
    public ResponseEntity<Map<String, Object>> errorJob(@PathVariable String id, @RequestBody Map<String, String> request) {
        String errorMessage = request.getOrDefault("errorMessage", "Unknown error");
        boolean success = jobService.errorJob(id, errorMessage);
        return ResponseEntity.ok(Map.of("success", success, "message",
            success ? "Job error recorded" : "Failed to record job error"));
    }

    /**
     * 重置任务
     */
    @PostMapping("/{id}/reset")
    public ResponseEntity<Map<String, Object>> resetJob(@PathVariable String id) {
        boolean success = jobService.resetJob(id);
        return ResponseEntity.ok(Map.of("success", success, "message",
            success ? "Job reset successfully" : "Failed to reset job"));
    }

    /**
     * 重试任务
     */
    @PostMapping("/{id}/retry")
    public ResponseEntity<Map<String, Object>> retryJob(@PathVariable String id) {
        boolean success = jobService.retryJob(id);
        return ResponseEntity.ok(Map.of("success", success, "message",
            success ? "Job retry initiated" : "Failed to retry job"));
    }

    /**
     * 编辑任务
     */
    @PostMapping("/{id}/edit")
    public ResponseEntity<Map<String, Object>> editJob(@PathVariable String id) {
        boolean success = jobService.editJob(id);
        return ResponseEntity.ok(Map.of("success", success, "message",
            success ? "Job returned to editing state" : "Failed to edit job"));
    }

    /**
     * 根据状态获取任务
     */
    @GetMapping("/state/{state}")
    public ResponseEntity<List<Job>> getJobsByState(@PathVariable JobState state) {
        List<Job> jobs = jobService.getJobsByState(state);
        return ResponseEntity.ok(jobs);
    }

    /**
     * 创建任务请求DTO
     */
    public static class CreateJobRequest {
        private String name;
        private String description;
        private String creator;

        // Getters and Setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getCreator() {
            return creator;
        }

        public void setCreator(String creator) {
            this.creator = creator;
        }
    }
}

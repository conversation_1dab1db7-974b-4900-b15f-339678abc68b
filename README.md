# FMS - Job状态机管理系统

基于Spring Boot和Spring State Machine实现的Job生命周期管理系统，支持状态机持久化到MongoDB。

## 功能特性

- ✅ 基于Spring State Machine的Job状态管理
- ✅ 状态机持久化到MongoDB
- ✅ 完整的Job生命周期控制
- ✅ RESTful API接口
- ✅ 错误处理和重试机制
- ✅ 单元测试和集成测试

## 技术栈

- **Spring Boot 3.5.4** - 应用框架
- **Spring State Machine 4.0.0** - 状态机框架
- **Spring Data MongoDB** - MongoDB数据访问
- **JUnit 5** - 单元测试
- **Testcontainers** - 集成测试
- **Java 21** - 编程语言

## Job状态定义

| 状态 | 描述 |
|------|------|
| `EDITING` | 编辑中（初始状态） |
| `SCHEDULE_FAILED` | 调度失败 |
| `WAITING_START` | 启动中 |
| `WAITING_NEXT_RUN` | 等待下次运行 |
| `RUNNING` | 运行中 |
| `STOPPING` | 停止中 |
| `COMPLETE` | 运行完成 |
| `STOPPED` | 已停止 |
| `ERROR` | 错误 |
| `RESETTING` | 重置中 |
| `RESET_FAILED` | 重置失败 |
| `DELETED` | 已删除 |

## 状态转换事件

| 事件 | 描述 |
|------|------|
| `SUBMIT` | 提交任务 |
| `START` | 启动任务 |
| `SCHEDULE_SUCCESS` | 调度成功 |
| `SCHEDULE_FAIL` | 调度失败 |
| `RUN` | 任务开始运行 |
| `FINISH` | 任务运行完成 |
| `STOP` | 停止任务 |
| `STOP_COMPLETE` | 任务停止完成 |
| `ERROR` | 任务发生错误 |
| `RESET` | 重置任务 |
| `RESET_COMPLETE` | 重置完成 |
| `RESET_FAIL` | 重置失败 |
| `DELETE` | 删除任务 |
| `EDIT` | 重新编辑 |
| `RETRY` | 重试 |

## API接口

### 基础CRUD操作

```bash
# 获取所有任务
GET /api/job

# 根据ID获取任务
GET /api/job/{id}

# 创建新任务
POST /api/job
{
  "name": "任务名称",
  "description": "任务描述",
  "creator": "创建者"
}

# 更新任务
PUT /api/job/{id}

# 删除任务
DELETE /api/job/{id}
```

### 状态机操作

```bash
# 提交任务
POST /api/job/{id}/submit

# 启动任务
POST /api/job/{id}/start

# 停止任务
POST /api/job/{id}/stop

# 完成任务
POST /api/job/{id}/complete

# 任务出错
POST /api/job/{id}/error
{
  "errorMessage": "错误信息"
}

# 重置任务
POST /api/job/{id}/reset

# 重试任务
POST /api/job/{id}/retry

# 编辑任务
POST /api/job/{id}/edit
```

### 查询操作

```bash
# 根据状态查询任务
GET /api/job/state/{state}
```

## 快速开始

### 1. 环境要求

- Java 21+
- Maven 3.6+
- MongoDB 4.4+

### 2. 启动MongoDB

```bash
# 使用Docker启动MongoDB
docker run -d --name mongodb -p 27017:27017 mongo:4.4.6
```

### 3. 运行应用

```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 启动应用
mvn spring-boot:run
```

### 4. 测试API

应用启动后，可以通过以下方式测试：

```bash
# 创建任务
curl -X POST http://localhost:8080/api/job \
  -H "Content-Type: application/json" \
  -d '{"name":"测试任务","description":"测试描述","creator":"test-user"}'

# 提交任务（假设任务ID为job-id）
curl -X POST http://localhost:8080/api/job/job-id/submit

# 启动任务
curl -X POST http://localhost:8080/api/job/job-id/start

# 查看任务状态
curl http://localhost:8080/api/job/job-id
```

## 项目结构

```
src/main/java/com/qz/hare/fms/job/
├── Job.java                           # Job实体类
├── JobState.java                      # 状态枚举
├── JobEvent.java                      # 事件枚举
├── JobRepository.java                 # 数据访问层
├── JobService.java                    # 业务逻辑层
├── JobController.java                 # 控制器层
├── JobStateMachineConfig.java         # 状态机配置
├── JobStateMachinePersistConfig.java  # 持久化配置
├── JobStateMachineStatePersist.java   # 状态持久化实现
└── JobStateMachineDemo.java           # 演示程序
```

## 测试

项目包含完整的测试套件：

```bash
# 运行所有测试
mvn test

# 运行单元测试
mvn test -Dtest=JobServiceTest

# 运行控制器测试
mvn test -Dtest=JobControllerTest

# 运行集成测试（需要Docker）
mvn test -Dtest=JobStateMachineIntegrationTest
```

## 配置

### application.yml

```yaml
spring:
  application:
    name: fms
  data:
    mongodb:
      uri: mongodb://localhost:27017/fms
      database: fms
  statemachine:
    data:
      mongo:
        collection: statemachine
```

## 许可证

MIT License

package com.qz.hare.fms.job;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Job实体类 - MongoDB文档
 * 
 * <AUTHOR>
 * create at 2025/8/19
 */
@Document(collection = "jobs")
public class Job {
    
    @Id
    private String id;
    
    /**
     * 任务名称
     */
    private String name;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 当前状态
     */
    private JobState currentState;
    
    /**
     * 状态机ID - 用于状态机持久化
     */
    private String stateMachineId;
    
    /**
     * 任务类型
     */
    private String jobType;
    
    /**
     * 任务配置参数
     */
    private Map<String, Object> config;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 最后运行时间
     */
    private LocalDateTime lastRunTime;
    
    /**
     * 下次运行时间
     */
    private LocalDateTime nextRunTime;
    
    /**
     * 创建者
     */
    private String creator;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 运行次数
     */
    private Integer runCount = 0;
    
    // 构造函数
    public Job() {
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        this.currentState = JobState.EDITING;
        this.runCount = 0;
    }
    
    public Job(String name, String description) {
        this();
        this.name = name;
        this.description = description;
    }
    
    // Getter and Setter methods
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public JobState getCurrentState() {
        return currentState;
    }
    
    public void setCurrentState(JobState currentState) {
        this.currentState = currentState;
        this.updateTime = LocalDateTime.now();
    }
    
    public String getStateMachineId() {
        return stateMachineId;
    }
    
    public void setStateMachineId(String stateMachineId) {
        this.stateMachineId = stateMachineId;
    }
    
    public String getJobType() {
        return jobType;
    }
    
    public void setJobType(String jobType) {
        this.jobType = jobType;
    }
    
    public Map<String, Object> getConfig() {
        return config;
    }
    
    public void setConfig(Map<String, Object> config) {
        this.config = config;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public LocalDateTime getLastRunTime() {
        return lastRunTime;
    }
    
    public void setLastRunTime(LocalDateTime lastRunTime) {
        this.lastRunTime = lastRunTime;
    }
    
    public LocalDateTime getNextRunTime() {
        return nextRunTime;
    }
    
    public void setNextRunTime(LocalDateTime nextRunTime) {
        this.nextRunTime = nextRunTime;
    }
    
    public String getCreator() {
        return creator;
    }
    
    public void setCreator(String creator) {
        this.creator = creator;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public Integer getRunCount() {
        return runCount;
    }
    
    public void setRunCount(Integer runCount) {
        this.runCount = runCount;
    }
    
    /**
     * 增加运行次数
     */
    public void incrementRunCount() {
        this.runCount++;
        this.updateTime = LocalDateTime.now();
    }
    
    @Override
    public String toString() {
        return "Job{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", currentState=" + currentState +
                ", stateMachineId='" + stateMachineId + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}

package com.qz.hare.fms.job;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.statemachine.persist.DefaultStateMachinePersister;

/**
 * 状态机持久化配置
 *
 * <AUTHOR>
 * create at 2025/8/19
 */
@Configuration
public class JobStateMachinePersistConfig {

    @Autowired
    private JobStateMachineStatePersist jobStateMachineStatePersist;

    /**
     * 状态机持久化器
     */
    @Bean
    public StateMachinePersister<JobState, JobEvent, String> stateMachinePersister() {
        return new DefaultStateMachinePersister<>(jobStateMachineStatePersist);
    }
}

package com.qz.hare.fms.job;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2025/8/19 14:50
 */
@RestController
@RequestMapping("/api/job")
public class JobController {

    @GetMapping
    public Object list() {
        return List.of(
                Map.of("name", "Job One", "status", "RUNNING"),
                Map.of("name", "Job Two", "status", "EDITING"));
    }
}

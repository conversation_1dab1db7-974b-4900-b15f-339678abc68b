package com.qz.hare.fms.job;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.statemachine.StateMachineContext;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.support.DefaultStateMachineContext;
import org.springframework.stereotype.Component;

/**
 * Job状态机状态持久化实现
 *
 * <AUTHOR>
 * create at 2025/8/19
 */
@Component
public class JobStateMachineStatePersist implements StateMachinePersist<JobState, JobEvent, String> {

    @Autowired
    private JobRepository jobRepository;

    @Override
    public void write(StateMachineContext<JobState, JobEvent> context, String contextObj) throws Exception {
        // 根据contextObj（jobId）查找Job并更新状态
        jobRepository.findById(contextObj).ifPresent(job -> {
            job.setCurrentState(context.getState());
            job.setStateMachineId(context.getId());
            jobRepository.save(job);
        });
    }

    @Override
    public StateMachineContext<JobState, JobEvent> read(String contextObj) throws Exception {
        // 根据contextObj（jobId）查找Job并返回状态机上下文
        return jobRepository.findById(contextObj)
            .map(job -> {
                return new DefaultStateMachineContext<JobState, JobEvent>(
                    job.getCurrentState(),
                    null,
                    null,
                    null,
                    null,
                    job.getStateMachineId()
                );
            })
            .orElse(null);
    }
}

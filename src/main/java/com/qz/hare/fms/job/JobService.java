package com.qz.hare.fms.job;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Job服务层
 * 
 * <AUTHOR>
 * create at 2025/8/19
 */
@Service
public class JobService {
    
    private static final Logger logger = LoggerFactory.getLogger(JobService.class);
    
    @Autowired
    private JobRepository jobRepository;
    
    @Autowired
    private StateMachineFactory<JobState, JobEvent> stateMachineFactory;
    
    @Autowired
    private StateMachinePersister<JobState, JobEvent, String> stateMachinePersister;
    
    /**
     * 创建新任务
     */
    public Job createJob(String name, String description, String creator) {
        Job job = new Job(name, description);
        job.setCreator(creator);
        job.setStateMachineId(UUID.randomUUID().toString());
        
        Job savedJob = jobRepository.save(job);
        logger.info("Created new job: {}", savedJob);
        
        return savedJob;
    }
    
    /**
     * 获取所有任务
     */
    public List<Job> getAllJobs() {
        return jobRepository.findAll();
    }
    
    /**
     * 根据ID获取任务
     */
    public Optional<Job> getJobById(String id) {
        return jobRepository.findById(id);
    }
    
    /**
     * 根据状态获取任务列表
     */
    public List<Job> getJobsByState(JobState state) {
        return jobRepository.findByCurrentState(state);
    }
    
    /**
     * 更新任务
     */
    public Job updateJob(Job job) {
        job.setUpdateTime(LocalDateTime.now());
        return jobRepository.save(job);
    }
    
    /**
     * 删除任务
     */
    public void deleteJob(String id) {
        jobRepository.deleteById(id);
        logger.info("Deleted job with id: {}", id);
    }
    
    /**
     * 发送事件到状态机
     */
    public boolean sendEvent(String jobId, JobEvent event) {
        try {
            Optional<Job> jobOpt = jobRepository.findById(jobId);
            if (jobOpt.isEmpty()) {
                logger.warn("Job not found: {}", jobId);
                return false;
            }
            
            Job job = jobOpt.get();
            StateMachine<JobState, JobEvent> stateMachine = stateMachineFactory.getStateMachine();
            
            // 恢复状态机状态
            stateMachinePersister.restore(stateMachine, jobId);
            
            // 发送事件
            boolean result = stateMachine.sendEvent(event);
            
            if (result) {
                // 持久化状态机状态
                stateMachinePersister.persist(stateMachine, jobId);
                
                // 更新Job状态
                job.setCurrentState(stateMachine.getState().getId());
                updateJob(job);
                
                logger.info("Event {} sent to job {}, new state: {}", event, jobId, job.getCurrentState());
            } else {
                logger.warn("Failed to send event {} to job {}", event, jobId);
            }
            
            return result;
        } catch (Exception e) {
            logger.error("Error sending event {} to job {}: {}", event, jobId, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 提交任务
     */
    public boolean submitJob(String jobId) {
        return sendEvent(jobId, JobEvent.SUBMIT);
    }
    
    /**
     * 启动任务
     */
    public boolean startJob(String jobId) {
        // 模拟调度成功
        if (sendEvent(jobId, JobEvent.SCHEDULE_SUCCESS)) {
            return sendEvent(jobId, JobEvent.RUN);
        }
        return false;
    }
    
    /**
     * 停止任务
     */
    public boolean stopJob(String jobId) {
        if (sendEvent(jobId, JobEvent.STOP)) {
            // 模拟停止完成
            return sendEvent(jobId, JobEvent.STOP_COMPLETE);
        }
        return false;
    }
    
    /**
     * 完成任务
     */
    public boolean completeJob(String jobId) {
        Optional<Job> jobOpt = getJobById(jobId);
        if (jobOpt.isPresent()) {
            Job job = jobOpt.get();
            job.incrementRunCount();
            job.setLastRunTime(LocalDateTime.now());
            updateJob(job);
        }
        return sendEvent(jobId, JobEvent.FINISH);
    }
    
    /**
     * 任务出错
     */
    public boolean errorJob(String jobId, String errorMessage) {
        Optional<Job> jobOpt = getJobById(jobId);
        if (jobOpt.isPresent()) {
            Job job = jobOpt.get();
            job.setErrorMessage(errorMessage);
            updateJob(job);
        }
        return sendEvent(jobId, JobEvent.ERROR);
    }
    
    /**
     * 重置任务
     */
    public boolean resetJob(String jobId) {
        if (sendEvent(jobId, JobEvent.RESET)) {
            // 模拟重置完成
            Optional<Job> jobOpt = getJobById(jobId);
            if (jobOpt.isPresent()) {
                Job job = jobOpt.get();
                job.setErrorMessage(null);
                job.setRunCount(0);
                job.setLastRunTime(null);
                updateJob(job);
            }
            return sendEvent(jobId, JobEvent.RESET_COMPLETE);
        }
        return false;
    }
    
    /**
     * 重试任务
     */
    public boolean retryJob(String jobId) {
        return sendEvent(jobId, JobEvent.RETRY);
    }
    
    /**
     * 编辑任务
     */
    public boolean editJob(String jobId) {
        return sendEvent(jobId, JobEvent.EDIT);
    }
    
    /**
     * 删除任务（状态机事件）
     */
    public boolean deleteJobByEvent(String jobId) {
        return sendEvent(jobId, JobEvent.DELETE);
    }
}

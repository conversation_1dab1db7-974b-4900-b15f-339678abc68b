package com.qz.hare.fms.job;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 并发性能演示
 * 
 * <AUTHOR>
 * create at 2025/8/19
 */
@Component
@Order(2) // 在JobStateMachineDemo之后运行
public class ConcurrencyDemo implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(ConcurrencyDemo.class);
    
    @Autowired
    private JobService originalJobService;
    
    @Autowired
    @Qualifier("concurrentJobService")
    private ConcurrentJobService concurrentJobService;
    
    @Autowired
    private JobRepository jobRepository;
    
    @Override
    public void run(String... args) throws Exception {
        logger.info("\n=== 并发性能演示开始 ===");
        
        // 演示并发问题
        demonstrateConcurrencyIssues();
        
        // 演示并发安全解决方案
        demonstrateConcurrencySafety();
        
        logger.info("=== 并发性能演示结束 ===\n");
    }
    
    /**
     * 演示原始实现的并发问题
     */
    private void demonstrateConcurrencyIssues() {
        logger.info("\n--- 演示原始实现的并发问题 ---");
        
        // 清理数据
        jobRepository.deleteAll();
        
        // 创建测试任务
        Job job = originalJobService.createJob("并发问题演示", "展示并发竞态条件", "demo-user");
        String jobId = job.getId();
        
        int threadCount = 10;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        
        long startTime = System.currentTimeMillis();
        
        // 多个线程同时提交任务
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    logger.info("线程 {} 尝试提交任务", threadId);
                    boolean result = originalJobService.submitJob(jobId);
                    if (result) {
                        successCount.incrementAndGet();
                        logger.info("线程 {} 提交成功", threadId);
                    } else {
                        failureCount.incrementAndGet();
                        logger.info("线程 {} 提交失败", threadId);
                    }
                } catch (Exception e) {
                    failureCount.incrementAndGet();
                    logger.error("线程 {} 发生异常: {}", threadId, e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            latch.await(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        long endTime = System.currentTimeMillis();
        executor.shutdown();
        
        // 检查最终状态
        Job finalJob = originalJobService.getJobById(jobId).orElse(null);
        
        logger.info("原始实现结果:");
        logger.info("  执行时间: {} ms", endTime - startTime);
        logger.info("  成功次数: {}", successCount.get());
        logger.info("  失败次数: {}", failureCount.get());
        logger.info("  最终状态: {}", finalJob != null ? finalJob.getCurrentState() : "null");
        logger.info("  期望结果: 只有1次成功，{} 次失败", threadCount - 1);
        
        if (successCount.get() > 1) {
            logger.warn("⚠️  检测到并发问题：多个线程同时成功修改了状态！");
        }
    }
    
    /**
     * 演示并发安全的解决方案
     */
    private void demonstrateConcurrencySafety() {
        logger.info("\n--- 演示并发安全解决方案 ---");
        
        // 创建测试任务
        Job job = concurrentJobService.createJob("并发安全演示", "展示并发安全实现", "demo-user");
        String jobId = job.getId();
        
        int threadCount = 10;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        
        long startTime = System.currentTimeMillis();
        
        // 多个线程同时提交任务
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    logger.info("线程 {} 尝试提交任务", threadId);
                    boolean result = concurrentJobService.submitJob(jobId);
                    if (result) {
                        successCount.incrementAndGet();
                        logger.info("线程 {} 提交成功", threadId);
                    } else {
                        failureCount.incrementAndGet();
                        logger.info("线程 {} 提交失败", threadId);
                    }
                } catch (Exception e) {
                    failureCount.incrementAndGet();
                    logger.error("线程 {} 发生异常: {}", threadId, e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            latch.await(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        long endTime = System.currentTimeMillis();
        executor.shutdown();
        
        // 检查最终状态
        Job finalJob = concurrentJobService.getJobById(jobId).orElse(null);
        
        logger.info("并发安全实现结果:");
        logger.info("  执行时间: {} ms", endTime - startTime);
        logger.info("  成功次数: {}", successCount.get());
        logger.info("  失败次数: {}", failureCount.get());
        logger.info("  最终状态: {}", finalJob != null ? finalJob.getCurrentState() : "null");
        logger.info("  版本号: {}", finalJob != null ? finalJob.getVersion() : "null");
        logger.info("  期望结果: 只有1次成功，{} 次失败", threadCount - 1);
        
        if (successCount.get() == 1 && failureCount.get() == threadCount - 1) {
            logger.info("✅ 并发安全实现正常工作！");
        } else {
            logger.warn("⚠️  并发安全实现可能存在问题");
        }
        
        // 演示复杂并发场景
        demonstrateComplexConcurrency(jobId);
    }
    
    /**
     * 演示复杂并发场景
     */
    private void demonstrateComplexConcurrency(String jobId) {
        logger.info("\n--- 演示复杂并发场景 ---");
        
        // 先启动任务
        concurrentJobService.startJob(jobId);
        
        int threadCount = 6;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        List<Future<String>> futures = new ArrayList<>();
        
        // 不同线程执行不同操作
        futures.add(executor.submit(() -> {
            boolean result = concurrentJobService.completeJob(jobId);
            return "COMPLETE: " + result;
        }));
        
        futures.add(executor.submit(() -> {
            boolean result = concurrentJobService.stopJob(jobId);
            return "STOP: " + result;
        }));
        
        futures.add(executor.submit(() -> {
            boolean result = concurrentJobService.errorJob(jobId, "并发错误测试");
            return "ERROR: " + result;
        }));
        
        futures.add(executor.submit(() -> {
            boolean result = concurrentJobService.completeJob(jobId);
            return "COMPLETE2: " + result;
        }));
        
        futures.add(executor.submit(() -> {
            boolean result = concurrentJobService.stopJob(jobId);
            return "STOP2: " + result;
        }));
        
        futures.add(executor.submit(() -> {
            boolean result = concurrentJobService.errorJob(jobId, "另一个并发错误");
            return "ERROR2: " + result;
        }));
        
        // 收集结果
        List<String> results = new ArrayList<>();
        for (Future<String> future : futures) {
            try {
                results.add(future.get(5, TimeUnit.SECONDS));
            } catch (Exception e) {
                results.add("EXCEPTION: " + e.getMessage());
            }
        }
        
        executor.shutdown();
        
        // 显示结果
        logger.info("复杂并发操作结果:");
        for (String result : results) {
            logger.info("  {}", result);
        }
        
        // 检查最终状态
        Job finalJob = concurrentJobService.getJobById(jobId).orElse(null);
        if (finalJob != null) {
            logger.info("最终状态: {}", finalJob.getCurrentState());
            logger.info("版本号: {}", finalJob.getVersion());
            logger.info("运行次数: {}", finalJob.getRunCount());
            logger.info("错误信息: {}", finalJob.getErrorMessage());
        }
    }
}

package com.qz.hare.fms.job;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Job状态机集成测试
 * 
 * <AUTHOR>
 * create at 2025/8/19
 */
@SpringBootTest
@Testcontainers
class JobStateMachineIntegrationTest {
    
    @Container
    static MongoDBContainer mongoDBContainer = new MongoDBContainer("mongo:4.4.6");
    
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.data.mongodb.uri", mongoDBContainer::getReplicaSetUrl);
    }
    
    @Autowired
    private JobService jobService;
    
    @Autowired
    private JobRepository jobRepository;
    
    private Job testJob;
    
    @BeforeEach
    void setUp() {
        // 清理数据库
        jobRepository.deleteAll();
        
        // 创建测试任务
        testJob = jobService.createJob("Integration Test Job", "Test Description", "test-user");
    }
    
    @Test
    void testJobLifecycle() {
        // 验证初始状态
        assertEquals(JobState.EDITING, testJob.getCurrentState());
        
        // 提交任务
        boolean submitResult = jobService.submitJob(testJob.getId());
        assertTrue(submitResult);
        
        // 验证状态变化
        Job updatedJob = jobService.getJobById(testJob.getId()).orElse(null);
        assertNotNull(updatedJob);
        assertEquals(JobState.WAITING_START, updatedJob.getCurrentState());
        
        // 启动任务
        boolean startResult = jobService.startJob(testJob.getId());
        assertTrue(startResult);
        
        // 验证运行状态
        updatedJob = jobService.getJobById(testJob.getId()).orElse(null);
        assertNotNull(updatedJob);
        assertEquals(JobState.RUNNING, updatedJob.getCurrentState());
        
        // 完成任务
        boolean completeResult = jobService.completeJob(testJob.getId());
        assertTrue(completeResult);
        
        // 验证完成状态
        updatedJob = jobService.getJobById(testJob.getId()).orElse(null);
        assertNotNull(updatedJob);
        assertEquals(JobState.COMPLETE, updatedJob.getCurrentState());
        assertEquals(1, updatedJob.getRunCount());
        assertNotNull(updatedJob.getLastRunTime());
    }
    
    @Test
    void testJobErrorHandling() {
        // 提交并启动任务
        jobService.submitJob(testJob.getId());
        jobService.startJob(testJob.getId());
        
        // 模拟任务出错
        String errorMessage = "Test error occurred";
        boolean errorResult = jobService.errorJob(testJob.getId(), errorMessage);
        assertTrue(errorResult);
        
        // 验证错误状态
        Job updatedJob = jobService.getJobById(testJob.getId()).orElse(null);
        assertNotNull(updatedJob);
        assertEquals(JobState.ERROR, updatedJob.getCurrentState());
        assertEquals(errorMessage, updatedJob.getErrorMessage());
        
        // 重试任务
        boolean retryResult = jobService.retryJob(testJob.getId());
        assertTrue(retryResult);
        
        // 验证重试后状态
        updatedJob = jobService.getJobById(testJob.getId()).orElse(null);
        assertNotNull(updatedJob);
        assertEquals(JobState.WAITING_START, updatedJob.getCurrentState());
    }
    
    @Test
    void testJobStopAndReset() {
        // 提交并启动任务
        jobService.submitJob(testJob.getId());
        jobService.startJob(testJob.getId());
        
        // 停止任务
        boolean stopResult = jobService.stopJob(testJob.getId());
        assertTrue(stopResult);
        
        // 验证停止状态
        Job updatedJob = jobService.getJobById(testJob.getId()).orElse(null);
        assertNotNull(updatedJob);
        assertEquals(JobState.STOPPED, updatedJob.getCurrentState());
        
        // 重置任务
        boolean resetResult = jobService.resetJob(testJob.getId());
        assertTrue(resetResult);
        
        // 验证重置后状态
        updatedJob = jobService.getJobById(testJob.getId()).orElse(null);
        assertNotNull(updatedJob);
        assertEquals(JobState.EDITING, updatedJob.getCurrentState());
        assertNull(updatedJob.getErrorMessage());
        assertEquals(0, updatedJob.getRunCount());
        assertNull(updatedJob.getLastRunTime());
    }
    
    @Test
    void testJobEditFromErrorState() {
        // 提交并启动任务
        jobService.submitJob(testJob.getId());
        jobService.startJob(testJob.getId());
        
        // 模拟任务出错
        jobService.errorJob(testJob.getId(), "Test error");
        
        // 从错误状态返回编辑状态
        boolean editResult = jobService.editJob(testJob.getId());
        assertTrue(editResult);
        
        // 验证编辑状态
        Job updatedJob = jobService.getJobById(testJob.getId()).orElse(null);
        assertNotNull(updatedJob);
        assertEquals(JobState.EDITING, updatedJob.getCurrentState());
    }
    
    @Test
    void testJobDeletion() {
        // 通过状态机事件删除任务
        boolean deleteResult = jobService.deleteJobByEvent(testJob.getId());
        assertTrue(deleteResult);
        
        // 验证删除状态
        Job updatedJob = jobService.getJobById(testJob.getId()).orElse(null);
        assertNotNull(updatedJob);
        assertEquals(JobState.DELETED, updatedJob.getCurrentState());
    }
    
    @Test
    void testGetJobsByState() {
        // 创建多个不同状态的任务
        Job job2 = jobService.createJob("Job 2", "Description 2", "test-user");
        Job job3 = jobService.createJob("Job 3", "Description 3", "test-user");
        
        // 改变一些任务的状态
        jobService.submitJob(job2.getId());
        jobService.submitJob(job3.getId());
        jobService.startJob(job3.getId());
        
        // 验证按状态查询
        assertEquals(1, jobService.getJobsByState(JobState.EDITING).size());
        assertEquals(1, jobService.getJobsByState(JobState.WAITING_START).size());
        assertEquals(1, jobService.getJobsByState(JobState.RUNNING).size());
    }
}
